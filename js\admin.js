// Admin Panel JavaScript

// Sample data - In real application, this would come from backend API
let products = [
    {
        id: 1,
        name: "Fresh Orange",
        category: "fruits",
        price: 12.99,
        stock: 50,
        status: "active",
        image: "image/product-1.png"
    },
    {
        id: 2,
        name: "Fresh Onion",
        category: "vegetables",
        price: 15.99,
        stock: 30,
        status: "active",
        image: "image/product-2.png"
    },
    {
        id: 3,
        name: "Fresh Meat",
        category: "meat",
        price: 25.99,
        stock: 20,
        status: "active",
        image: "image/product-3.png"
    },
    {
        id: 4,
        name: "Fresh Cabbage",
        category: "vegetables",
        price: 8.99,
        stock: 40,
        status: "active",
        image: "image/product-4.png"
    },
    {
        id: 5,
        name: "Fresh Potato",
        category: "vegetables",
        price: 5.99,
        stock: 60,
        status: "active",
        image: "image/product-5.png"
    },
    {
        id: 6,
        name: "Fresh Avocado",
        category: "fruits",
        price: 18.99,
        stock: 25,
        status: "active",
        image: "image/product-6.png"
    }
];

let orders = [
    {
        id: "ORD001",
        customer: "<PERSON>",
        date: "2024-01-15",
        total: 45.99,
        status: "delivered"
    },
    {
        id: "ORD002",
        customer: "<PERSON>",
        date: "2024-01-16",
        total: 32.50,
        status: "processing"
    },
    {
        id: "ORD003",
        customer: "Mike <PERSON>",
        date: "2024-01-17",
        total: 78.25,
        status: "pending"
    }
];

let customers = [
    {
        id: 1,
        name: "John Doe",
        email: "<EMAIL>",
        phone: "+1234567890",
        orders: 5,
        totalSpent: 245.99,
        status: "active"
    },
    {
        id: 2,
        name: "Jane Smith",
        email: "<EMAIL>",
        phone: "+1234567891",
        orders: 3,
        totalSpent: 156.75,
        status: "active"
    }
];

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for auth.js to load and set currentUser
    setTimeout(() => {
        checkAdminAccess();
    }, 100);

    updateDashboardStats();
    loadProducts();
    loadOrders();
    loadCustomers();
    initializeEventListeners();

    // Initialize management features if they exist
    if (typeof loadBlogs === 'function') {
        loadBlogs();
    }
    if (typeof loadReviews === 'function') {
        loadReviews();
    }
});

function checkAdminAccess() {
    // Check if user is logged in and is admin
    if (!window.currentUser) {
        showAdminLoginPrompt();
        return;
    }

    if (window.currentUser.role !== 'admin') {
        showNotification('Access denied. Admin privileges required.', 'error');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
        return;
    }

    // User is admin, show admin content
    showAdminContent();
}

function showAdminLoginPrompt() {
    const adminDashboard = document.querySelector('.admin-dashboard');
    if (adminDashboard) {
        adminDashboard.innerHTML = `
            <div class="admin-login-prompt">
                <div class="login-container">
                    <div class="login-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h2>Admin Access Required</h2>
                    <p>Please login with admin credentials to access the admin panel.</p>
                    <div class="admin-demo-login">
                        <h3>Demo Admin Credentials:</h3>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Password:</strong> admin123</p>
                    </div>
                    <div class="login-actions">
                        <a href="login.html" class="btn">
                            <i class="fas fa-sign-in-alt"></i> Login as Admin
                        </a>
                        <button class="btn secondary" onclick="quickAdminLogin()">
                            <i class="fas fa-bolt"></i> Quick Demo Login
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
}

function showAdminContent() {
    // Admin is logged in, ensure all content is visible
    const adminDashboard = document.querySelector('.admin-dashboard');
    if (adminDashboard && adminDashboard.querySelector('.admin-login-prompt')) {
        // Reload the page to show admin content
        location.reload();
    }
}

function quickAdminLogin() {
    // Quick login for demo purposes
    const adminUser = {
        id: 2,
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'admin',
        phone: '+1234567891',
        address: '456 Admin Ave, City, State 12345',
        joinDate: '2024-01-01',
        isActive: true
    };

    window.currentUser = adminUser;
    localStorage.setItem('currentUser', JSON.stringify(adminUser));

    // Update UI
    if (window.updateUserInterface) {
        window.updateUserInterface();
    }

    showNotification('Admin login successful!', 'success');

    // Show admin content
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Update dashboard statistics
function updateDashboardStats() {
    document.getElementById('total-products').textContent = products.length;
    document.getElementById('total-orders').textContent = orders.length;
    document.getElementById('total-customers').textContent = customers.length;
    
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    document.getElementById('total-revenue').textContent = '$' + totalRevenue.toFixed(2);
}

// Tab switching functionality
function initializeEventListeners() {
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            switchTab(tabName);
        });
    });

    // Add product modal
    const addProductBtn = document.getElementById('add-product-btn');
    const modal = document.getElementById('add-product-modal');
    const closeBtn = modal.querySelector('.close');
    const cancelBtn = modal.querySelector('.btn-cancel');

    addProductBtn.addEventListener('click', () => {
        modal.style.display = 'block';
    });

    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    cancelBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Add product form submission
    document.getElementById('add-product-form').addEventListener('submit', handleAddProduct);
}

function switchTab(tabName) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // Add active class to selected tab and content
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// Load products into table
function loadProducts() {
    const tbody = document.getElementById('products-tbody');
    tbody.innerHTML = '';

    products.forEach(product => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${product.id}</td>
            <td><img src="${product.image}" alt="${product.name}"></td>
            <td>${product.name}</td>
            <td>${product.category}</td>
            <td>$${product.price}</td>
            <td>${product.stock}</td>
            <td><span class="status-badge status-${product.status}">${product.status}</span></td>
            <td>
                <button class="action-btn btn-edit" onclick="editProduct(${product.id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn btn-delete" onclick="deleteProduct(${product.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Load orders into table
function loadOrders() {
    const tbody = document.getElementById('orders-tbody');
    tbody.innerHTML = '';

    orders.forEach(order => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${order.id}</td>
            <td>${order.customer}</td>
            <td>${order.date}</td>
            <td>$${order.total}</td>
            <td><span class="status-badge status-${order.status}">${order.status}</span></td>
            <td>
                <button class="action-btn btn-view" onclick="viewOrder('${order.id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn btn-edit" onclick="editOrder('${order.id}')">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Load customers into table
function loadCustomers() {
    const tbody = document.getElementById('customers-tbody');
    tbody.innerHTML = '';

    customers.forEach(customer => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${customer.id}</td>
            <td>${customer.name}</td>
            <td>${customer.email}</td>
            <td>${customer.phone}</td>
            <td>${customer.orders}</td>
            <td>$${customer.totalSpent}</td>
            <td><span class="status-badge status-${customer.status}">${customer.status}</span></td>
            <td>
                <button class="action-btn btn-view" onclick="viewCustomer(${customer.id})">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn btn-edit" onclick="editCustomer(${customer.id})">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Handle add product form submission
function handleAddProduct(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const newProduct = {
        id: products.length + 1,
        name: formData.get('name'),
        category: formData.get('category'),
        price: parseFloat(formData.get('price')),
        stock: parseInt(formData.get('stock')),
        status: 'active',
        image: 'image/default-product.png' // In real app, handle file upload
    };

    products.push(newProduct);
    loadProducts();
    updateDashboardStats();
    
    // Close modal and reset form
    document.getElementById('add-product-modal').style.display = 'none';
    e.target.reset();
    
    alert('Product added successfully!');
}

// Product management functions
function editProduct(id) {
    const product = products.find(p => p.id === id);
    if (product) {
        // In real application, open edit modal with product data
        const newName = prompt('Enter new product name:', product.name);
        if (newName) {
            product.name = newName;
            loadProducts();
            alert('Product updated successfully!');
        }
    }
}

function deleteProduct(id) {
    if (confirm('Are you sure you want to delete this product?')) {
        products = products.filter(p => p.id !== id);
        loadProducts();
        updateDashboardStats();
        alert('Product deleted successfully!');
    }
}

// Order management functions
function viewOrder(id) {
    const order = orders.find(o => o.id === id);
    if (order) {
        alert(`Order Details:\nID: ${order.id}\nCustomer: ${order.customer}\nTotal: $${order.total}\nStatus: ${order.status}`);
    }
}

function editOrder(id) {
    const order = orders.find(o => o.id === id);
    if (order) {
        const newStatus = prompt('Enter new status (pending/processing/shipped/delivered):', order.status);
        if (newStatus && ['pending', 'processing', 'shipped', 'delivered'].includes(newStatus)) {
            order.status = newStatus;
            loadOrders();
            alert('Order status updated successfully!');
        }
    }
}

// Customer management functions
function viewCustomer(id) {
    const customer = customers.find(c => c.id === id);
    if (customer) {
        alert(`Customer Details:\nName: ${customer.name}\nEmail: ${customer.email}\nPhone: ${customer.phone}\nTotal Orders: ${customer.orders}\nTotal Spent: $${customer.totalSpent}`);
    }
}

function editCustomer(id) {
    const customer = customers.find(c => c.id === id);
    if (customer) {
        const newEmail = prompt('Enter new email:', customer.email);
        if (newEmail) {
            customer.email = newEmail;
            loadCustomers();
            alert('Customer updated successfully!');
        }
    }
}

// Export functions for global use
window.handleAddProduct = handleAddProduct;
window.editProduct = editProduct;
window.deleteProduct = deleteProduct;
window.viewOrder = viewOrder;
window.editOrder = editOrder;
window.viewCustomer = viewCustomer;
window.editCustomer = editCustomer;
window.quickAdminLogin = quickAdminLogin;
