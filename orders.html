<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>My Orders - Glossary Store</title>
	
	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	
	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">
	<link rel="stylesheet" type="text/css" href="css/auth.css">
</head>
<body>

<!-- header section  -->
<header class="header"> 
    <a href="index.html" class="logo"><i class="fas fa-shopping-basket"></i> Glossary Store</a>

<nav class="navbar">
	<a href="index.html">home</a>
	<a href="index.html#features">features</a>
	<a href="index.html#products">products</a>
	<a href="categories.html">categories</a>
	<a href="reviews.html">reviews</a>
	<a href="blogs.html">blogs</a>
	<a href="login.html" class="login-link">login</a>

	<div class="admin-section">
		<a href="admin.html" class="admin-panel-link" id="navbar-admin" style="display: none;">
			<i class="fas fa-cogs"></i> Admin Panel
		</a>
	</div>
</nav>

<div class="icons">
	<div class="fas fa-bars" id="menu-btn"></div>
	<div class="fas fa-search" id="search-btn"></div>
	<div class="fas fa-shopping-cart" id="cart-btn">
		<span class="cart-badge" id="cart-count">0</span>
	</div>
	<div class="fas fa-user" id="login-btn"></div>
</div>

<form class="search-form">
	<input type="search" id="search-box" placeholder="Search products...">
	<label for="search-box" class="fas fa-search"></label>
</form>

<div class="shopping-cart">
	<div id="cart-items">
		<p class="empty-cart">Your cart is empty</p>
	</div>
	<div class="Total" id="cart-total">Total: $0.00</div>
	<a href="checkout.html" class="btn" id="checkout-btn" style="display: none;">Checkout</a>
</div>

<!-- User Profile Dropdown -->
<div class="user-profile" id="user-profile">
	<div class="profile-info" id="profile-info">
		<div class="avatar">
			<i class="fas fa-user-circle"></i>
		</div>
		<div class="user-details">
			<h4 id="user-name">Guest User</h4>
			<p id="user-email">Please login</p>
		</div>
	</div>
	<div class="profile-menu">
		<a href="profile.html"><i class="fas fa-user"></i> My Profile</a>
		<a href="orders.html" class="active"><i class="fas fa-shopping-bag"></i> My Orders</a>
		<a href="wishlist.html"><i class="fas fa-heart"></i> Wishlist</a>
		<a href="addresses.html"><i class="fas fa-map-marker-alt"></i> Addresses</a>
		<a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
	</div>
</div>

</header>
<!-- header section  -->

<!-- orders section -->
<section class="orders-section" style="padding-top: 12rem;">
	<div class="container">
		<h1 class="heading">My <span>Orders</span></h1>
		
		<div class="orders-container" id="orders-container">
			<div class="no-orders">
				<i class="fas fa-shopping-bag"></i>
				<h3>No Orders Yet</h3>
				<p>You haven't placed any orders yet. Start shopping to see your orders here!</p>
				<a href="index.html" class="btn">Start Shopping</a>
			</div>
		</div>
	</div>
</section>

<script src="js/script.js"></script>
<script src="js/auth.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    if (!window.currentUser) {
        showNotification('Please login to view your orders', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return;
    }
    
    loadUserOrders();
});

function loadUserOrders() {
    const orders = JSON.parse(localStorage.getItem('orders')) || [];
    const userOrders = orders.filter(order => order.customerId === window.currentUser.id);
    const container = document.getElementById('orders-container');
    
    if (userOrders.length === 0) {
        return; // Show default no orders message
    }
    
    container.innerHTML = '';
    
    userOrders.forEach(order => {
        const orderElement = document.createElement('div');
        orderElement.className = 'order-card';
        orderElement.innerHTML = `
            <div class="order-header">
                <div class="order-info">
                    <h3>Order ${order.orderNumber}</h3>
                    <p>Placed on ${new Date(order.orderDate).toLocaleDateString()}</p>
                </div>
                <div class="order-status status-${order.status}">
                    ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </div>
            </div>
            <div class="order-items">
                ${order.items.map(item => `
                    <div class="order-item">
                        <img src="${item.image}" alt="${item.name}">
                        <div class="item-details">
                            <h4>${item.name}</h4>
                            <p>Qty: ${item.quantity} × $${item.price}</p>
                        </div>
                        <div class="item-total">$${(item.quantity * item.price).toFixed(2)}</div>
                    </div>
                `).join('')}
            </div>
            <div class="order-footer">
                <div class="order-total">Total: $${order.total.toFixed(2)}</div>
                <div class="order-actions">
                    <button class="btn secondary" onclick="trackOrder('${order.orderNumber}')">Track Order</button>
                    <button class="btn" onclick="reorderItems('${order.orderNumber}')">Reorder</button>
                </div>
            </div>
        `;
        container.appendChild(orderElement);
    });
}

function trackOrder(orderNumber) {
    showNotification(`Tracking information for order ${orderNumber} will be available soon!`, 'info');
}

function reorderItems(orderNumber) {
    const orders = JSON.parse(localStorage.getItem('orders')) || [];
    const order = orders.find(o => o.orderNumber === orderNumber);
    
    if (order) {
        // Add all items from this order to cart
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        
        order.items.forEach(item => {
            const existingItem = cart.find(cartItem => cartItem.id === item.id);
            if (existingItem) {
                existingItem.quantity += item.quantity;
            } else {
                cart.push({
                    id: item.id,
                    name: item.name,
                    price: item.price,
                    image: item.image,
                    quantity: item.quantity,
                    addedAt: new Date().toISOString()
                });
            }
        });
        
        localStorage.setItem('cart', JSON.stringify(cart));
        
        if (window.updateCartDisplay) {
            window.updateCartDisplay();
        }
        
        showNotification('Items added to cart!', 'success');
    }
}
</script>

<style>
.orders-section {
    min-height: 100vh;
    background: #f8f9fa;
    padding: 4rem 2rem;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
}

.no-orders {
    text-align: center;
    padding: 4rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.1);
}

.no-orders i {
    font-size: 6rem;
    color: var(--light-color);
    margin-bottom: 2rem;
}

.no-orders h3 {
    font-size: 2.5rem;
    color: var(--black);
    margin-bottom: 1rem;
}

.no-orders p {
    font-size: 1.6rem;
    color: var(--light-color);
    margin-bottom: 2rem;
}

.order-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid #e1e5e9;
}

.order-info h3 {
    font-size: 2rem;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.order-info p {
    color: var(--light-color);
    font-size: 1.4rem;
}

.order-status {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-processing {
    background: #fff3cd;
    color: #856404;
}

.status-shipped {
    background: #d1ecf1;
    color: #0c5460;
}

.status-delivered {
    background: #d4edda;
    color: #155724;
}

.order-items {
    padding: 2rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.order-item img {
    width: 6rem;
    height: 6rem;
    object-fit: cover;
    border-radius: 0.5rem;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    font-size: 1.6rem;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.item-details p {
    color: var(--light-color);
    font-size: 1.4rem;
}

.item-total {
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--green);
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: #f8f9fa;
}

.order-total {
    font-size: 2rem;
    font-weight: 600;
    color: var(--black);
}

.order-actions {
    display: flex;
    gap: 1rem;
}

.btn.secondary {
    background: #6c757d;
    color: white;
}

.btn.secondary:hover {
    background: #545b62;
}

@media (max-width: 768px) {
    .order-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .order-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .order-actions {
        width: 100%;
        justify-content: center;
    }
}
</style>

</body>
</html>
