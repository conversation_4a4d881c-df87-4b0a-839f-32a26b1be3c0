<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Categories - Glossary Store</title>
	
	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	
	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">
</head>
<body>

<!-- header section  -->
<header class="header"> 
    <a href="index.html" class="logo"><i class="fas fa-shopping-basket"></i> Glossary Store</a>

<nav class="navbar">
	<a href="index.html">home</a>
	<a href="index.html#features">features</a>
	<a href="index.html#products">products</a>
	<a href="categories.html" class="active">categories</a>
	<a href="reviews.html">reviews</a>
	<a href="blogs.html">blogs</a>
	<a href="login.html" class="login-link" id="navbar-login">login</a>

	<div class="admin-section">
		<a href="admin.html" class="admin-panel-link" id="navbar-admin" style="display: none;">
			<i class="fas fa-cogs"></i> Admin Panel
		</a>
	</div>
</nav>

<div class="icons">
	<div class="fas fa-bars" id="menu-btn" title="Menu">
		<i class="fas fa-bars"></i>
	</div>
	<div class="fas fa-search" id="search-btn" title="Search Products">
		<i class="fas fa-search"></i>
	</div>
	<div class="fas fa-shopping-cart" id="cart-btn" title="Shopping Cart">
		<i class="fas fa-shopping-cart"></i>
		<span class="cart-count" id="cart-count">0</span>
	</div>
	<div class="fas fa-user" id="login-btn" title="User Account">
		<i class="fas fa-user"></i>
	</div>
</div>

<form class="search-form">
	<input type="search" id="search-box" placeholder="Search categories...">
	<label for="search-box" class="fas fa-search"></label>
</form>

<div class="shopping-cart">
	<div id="cart-items">
		<p class="empty-cart">Your cart is empty</p>
	</div>
	<div class="Total" id="cart-total">Total: $0.00</div>
	<a href="checkout.html" class="btn" id="checkout-btn" style="display: none;">Checkout</a>
</div>



</header>
<!-- header section  -->

<!-- categories section  -->
<section class="categories" id="categories" style="padding-top: 12rem;">
	<h1 class="heading"> product <span>categories</span></h1>

    <div class="box-container">
    	<div class="box">
    		<img src="image/cat-1.png" alt="Fruits">
    		<h3>Fresh Fruits</h3>
    		<p>Organic and fresh fruits delivered to your doorstep</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/cat-2.png" alt="Vegetables">
    		<h3>Fresh Vegetables</h3>
    		<p>Farm fresh vegetables for healthy living</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/cat-3.png" alt="Dairy">
    		<h3>Dairy Products</h3>
    		<p>Pure and fresh dairy products from trusted sources</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/cat-4.png" alt="Meat">
    		<h3>Fresh Meat</h3>
    		<p>Premium quality meat and poultry products</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/product-7.png" alt="Grains">
    		<h3>Grains & Cereals</h3>
    		<p>Nutritious grains and cereals for healthy meals</p>
    		<a href="#" class="btn">shop now</a>
    	</div>

    	<div class="box">
    		<img src="image/product-8.png" alt="Beverages">
    		<h3>Beverages</h3>
    		<p>Refreshing drinks and healthy beverages</p>
    		<a href="#" class="btn">shop now</a>
    	</div>
    </div>

</section>
<!-- categories section  -->

<!-- featured products by category -->
<section class="featured-products">
	<h1 class="heading"> featured <span>products</span></h1>
	
	<div class="category-tabs">
		<button class="tab-btn active" data-category="fruits">Fruits</button>
		<button class="tab-btn" data-category="vegetables">Vegetables</button>
		<button class="tab-btn" data-category="dairy">Dairy</button>
		<button class="tab-btn" data-category="meat">Meat</button>
	</div>

	<div class="products-grid" id="products-grid">
		<!-- Products will be loaded here dynamically -->
		<div class="product-box" data-product-id="1">
			<img src="image/product-1.png" alt="Orange">
			<h3>Fresh Orange</h3>
			<div class="price">$12.99</div>
			<div class="stars">
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star-half-alt"></i>
			</div>
			<a href="#" class="btn" onclick="addToCart(1)" data-product-id="1">
				<i class="fas fa-shopping-cart"></i> add to cart
			</a>
		</div>

		<div class="product-box" data-product-id="6">
			<img src="image/product-6.png" alt="Avocado">
			<h3>Fresh Avocado</h3>
			<div class="price">$15.99</div>
			<div class="stars">
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
			</div>
			<a href="#" class="btn" onclick="addToCart(6)" data-product-id="6">
				<i class="fas fa-shopping-cart"></i> add to cart
			</a>
		</div>

		<div class="product-box" data-product-id="7">
			<img src="image/product-1.png" alt="Apple">
			<h3>Red Apple</h3>
			<div class="price">$8.99</div>
			<div class="stars">
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="fas fa-star"></i>
				<i class="far fa-star"></i>
			</div>
			<a href="#" class="btn" onclick="addToCart(7)" data-product-id="7">
				<i class="fas fa-shopping-cart"></i> add to cart
			</a>
		</div>
	</div>

	<div class="scroll-controls">
		<button class="scroll-btn" id="scroll-left">
			<i class="fas fa-chevron-left"></i>
		</button>
		<button class="scroll-btn" id="scroll-right">
			<i class="fas fa-chevron-right"></i>
		</button>
	</div>
</section>

<script src="js/script.js"></script>
<script src="js/auth.js"></script>
<script>
// Category tab functionality
document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // Remove active class from all buttons
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        this.classList.add('active');

        // Here you would typically load products for the selected category
        const category = this.dataset.category;
        console.log('Loading products for category:', category);
    });
});

// Horizontal scroll functionality
document.addEventListener('DOMContentLoaded', function() {
    const productsGrid = document.getElementById('products-grid');
    const scrollLeftBtn = document.getElementById('scroll-left');
    const scrollRightBtn = document.getElementById('scroll-right');

    if (productsGrid && scrollLeftBtn && scrollRightBtn) {
        const scrollAmount = 300; // pixels to scroll

        scrollLeftBtn.addEventListener('click', () => {
            productsGrid.scrollBy({
                left: -scrollAmount,
                behavior: 'smooth'
            });
        });

        scrollRightBtn.addEventListener('click', () => {
            productsGrid.scrollBy({
                left: scrollAmount,
                behavior: 'smooth'
            });
        });

        // Update button states based on scroll position
        function updateScrollButtons() {
            const isAtStart = productsGrid.scrollLeft <= 0;
            const isAtEnd = productsGrid.scrollLeft >= (productsGrid.scrollWidth - productsGrid.clientWidth);

            scrollLeftBtn.disabled = isAtStart;
            scrollRightBtn.disabled = isAtEnd;
        }

        // Initial button state
        updateScrollButtons();

        // Update button states on scroll
        productsGrid.addEventListener('scroll', updateScrollButtons);
    }
});
</script>

<!-- Main JavaScript for cart functionality -->
<script src="js/script.js"></script>

</body>
</html>
