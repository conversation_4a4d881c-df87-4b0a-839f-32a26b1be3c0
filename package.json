{"name": "glossary-store", "version": "1.0.0", "description": "A modern grocery store website with Node.js backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["grocery", "store", "nodejs", "express", "ecommerce"], "author": "Glossary Store Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "multer": "^1.4.5-lts.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}