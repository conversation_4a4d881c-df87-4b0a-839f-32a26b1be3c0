# Glossary Store - Modern Grocery E-commerce Website

A complete grocery store website with frontend and backend functionality, featuring product management, user reviews, blogs, and admin panel.

## Features

### Frontend
- **Responsive Design**: Mobile-first approach with modern UI/UX
- **Home Page**: Hero section, features, and product showcase
- **Categories Page**: Product categories with filtering
- **Reviews Page**: Customer reviews and rating system
- **Blogs Page**: Articles about healthy living and grocery tips
- **Admin Panel**: Product management and analytics dashboard

### Backend (Node.js)
- **RESTful API**: Complete API for products, orders, customers, reviews, and blogs
- **JSON Database**: File-based storage for easy deployment
- **Product Management**: CRUD operations for products
- **Order Processing**: Order management system
- **Customer Management**: User data and analytics
- **Review System**: Customer feedback and ratings

## File Structure

```
glossary-store/
├── index.html              # Home page
├── categories.html         # Categories page
├── reviews.html           # Reviews page
├── blogs.html             # Blogs page
├── admin.html             # Admin panel
├── css/
│   ├── style.css          # Main styles
│   └── admin.css          # Admin panel styles
├── js/
│   ├── script.js          # Main JavaScript
│   └── admin.js           # Admin functionality
├── image/                 # Product and UI images
├── server.js              # Node.js backend server
├── package.json           # Node.js dependencies
├── data/                  # JSON database files
│   ├── products.json      # Products data
│   ├── orders.json        # Orders data
│   ├── customers.json     # Customers data
│   ├── reviews.json       # Reviews data
│   └── blogs.json         # Blogs data
└── README.md              # This file
```

## Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- npm (Node Package Manager)

### Installation Steps

1. **Clone or download the project**
   ```bash
   cd glossary-store
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the server**
   ```bash
   npm start
   ```
   
   For development with auto-restart:
   ```bash
   npm run dev
   ```

4. **Access the website**
   - Main website: http://localhost:3000
   - Admin panel: http://localhost:3000/admin

## API Endpoints

### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Add new product
- `GET /api/products?category=fruits` - Filter by category
- `GET /api/products?search=orange` - Search products

### Orders
- `GET /api/orders` - Get all orders
- `POST /api/orders` - Create new order

### Reviews
- `GET /api/reviews` - Get all reviews
- `POST /api/reviews` - Add new review
- `GET /api/reviews?productId=1` - Get reviews for specific product

### Blogs
- `GET /api/blogs` - Get all published blogs

### Customers
- `GET /api/customers` - Get all customers

## Features Overview

### Navigation
- **Working navbar** with proper page linking
- **Responsive menu** for mobile devices
- **Search functionality** across all pages
- **Shopping cart** with item management
- **User login** modal

### Product Management
- **Product catalog** with categories
- **Product filtering** by category and search
- **Product details** with ratings and reviews
- **Stock management** in admin panel
- **Add/Edit/Delete** products via admin

### Admin Panel
- **Dashboard** with statistics
- **Product management** with full CRUD operations
- **Order tracking** and status updates
- **Customer management** and analytics
- **Sales analytics** and reporting

### User Experience
- **Responsive design** works on all devices
- **Fast loading** with optimized images
- **Interactive elements** with smooth animations
- **User-friendly** interface with clear navigation

## Technologies Used

### Frontend
- HTML5, CSS3, JavaScript (ES6+)
- Font Awesome icons
- Swiper.js for product sliders
- Responsive Grid and Flexbox layouts

### Backend
- Node.js with Express.js
- JSON file-based database
- CORS enabled for cross-origin requests
- RESTful API architecture

## Customization

### Adding New Products
1. Use the admin panel at `/admin`
2. Click "Add New Product"
3. Fill in product details
4. Product will be added to the JSON database

### Modifying Styles
- Edit `css/style.css` for main styles
- Edit `css/admin.css` for admin panel styles
- All styles use CSS custom properties for easy theming

### Adding New Pages
1. Create new HTML file
2. Include the header section from existing pages
3. Add navigation link in all page headers
4. Style the new page in CSS

## Deployment

### Local Development
- Run `npm start` to start the server
- Access at http://localhost:3000

### Production Deployment
1. Set environment variables
2. Configure production database
3. Deploy to hosting service (Heroku, Vercel, etc.)
4. Update API endpoints if needed

## Support

For issues or questions:
1. Check the console for error messages
2. Verify all files are in correct locations
3. Ensure Node.js dependencies are installed
4. Check that the server is running on the correct port

## License

This project is open source and available under the MIT License.
