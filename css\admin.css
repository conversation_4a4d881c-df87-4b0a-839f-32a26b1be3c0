/* Admin Panel Styles */

.admin-link {
    background: var(--green) !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem !important;
}

.admin-dashboard {
    min-height: 100vh;
    background: #f8f9fa;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card i {
    font-size: 3rem;
    color: var(--green);
    background: rgba(0, 128, 0, 0.1);
    padding: 1rem;
    border-radius: 50%;
    width: 6rem;
    height: 6rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-info h3 {
    font-size: 2.5rem;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.stat-info p {
    color: var(--light-color);
    font-size: 1.4rem;
}

/* Admin Tabs */
.admin-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid #eee;
}

.admin-tabs .tab-btn {
    background: none;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.6rem;
    color: var(--light-color);
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.admin-tabs .tab-btn.active {
    color: var(--green);
    border-bottom-color: var(--green);
}

.admin-tabs .tab-btn:hover {
    color: var(--green);
}

/* Tab Content */
.tab-content {
    display: none;
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
}

.tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.section-header h2 {
    font-size: 2.4rem;
    color: var(--black);
}

/* Tables */
.products-table, .orders-table, .customers-table {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

table th, table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

table th {
    background: #f8f9fa;
    font-weight: 600;
    color: var(--black);
    font-size: 1.4rem;
}

table td {
    font-size: 1.3rem;
    color: var(--light-color);
}

table img {
    width: 5rem;
    height: 5rem;
    object-fit: cover;
    border-radius: 0.5rem;
}

/* Status badges */
.status-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 2rem;
    font-size: 1.2rem;
    font-weight: 500;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-delivered {
    background: #d1ecf1;
    color: #0c5460;
}

/* Action buttons */
.action-btn {
    padding: 0.5rem 1rem;
    margin: 0 0.2rem;
    border: none;
    border-radius: 0.3rem;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #007bff;
    color: white;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-view {
    background: var(--green);
    color: white;
}

.action-btn:hover {
    opacity: 0.8;
    transform: translateY(-2px);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2.5rem;
    cursor: pointer;
    color: var(--light-color);
}

.close:hover {
    color: var(--black);
}

/* Form styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--black);
    font-size: 1.4rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 0.5rem;
    font-size: 1.4rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--green);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.btn-cancel {
    background: #6c757d;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 1.4rem;
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.chart-container {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
}

.chart-container h3 {
    font-size: 1.8rem;
    color: var(--black);
    margin-bottom: 1rem;
}

.chart-placeholder {
    height: 300px;
    background: #f8f9fa;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-color);
    font-size: 1.4rem;
}

/* Filters */
.filter-select, .search-input {
    padding: 0.8rem 1.2rem;
    border: 1px solid #ddd;
    border-radius: 0.5rem;
    font-size: 1.4rem;
}

/* Admin Login Prompt */
.admin-login-prompt {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 60vh;
	padding: 4rem 2rem;
}

.login-container {
	background: white;
	padding: 4rem;
	border-radius: 2rem;
	box-shadow: 0 2rem 4rem rgba(0,0,0,0.1);
	text-align: center;
	max-width: 500px;
	width: 100%;
}

.login-icon {
	font-size: 6rem;
	color: var(--green);
	margin-bottom: 2rem;
}

.login-container h2 {
	font-size: 3rem;
	color: var(--black);
	margin-bottom: 1rem;
}

.login-container p {
	font-size: 1.6rem;
	color: var(--light-color);
	margin-bottom: 2rem;
}

.admin-demo-login {
	background: #f8f9fa;
	padding: 2rem;
	border-radius: 1rem;
	margin: 2rem 0;
	border-left: 4px solid var(--green);
}

.admin-demo-login h3 {
	font-size: 1.8rem;
	color: var(--black);
	margin-bottom: 1rem;
}

.admin-demo-login p {
	font-size: 1.4rem;
	margin-bottom: 0.5rem;
	color: var(--light-color);
}

.login-actions {
	display: flex;
	gap: 1rem;
	justify-content: center;
	flex-wrap: wrap;
}

.login-actions .btn {
	padding: 1.5rem 2rem;
	font-size: 1.6rem;
	border-radius: 1rem;
	text-decoration: none;
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	transition: all 0.3s ease;
}

.login-actions .btn.secondary {
	background: #6c757d;
	color: white;
	border: none;
	cursor: pointer;
}

.login-actions .btn.secondary:hover {
	background: #545b62;
	transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .admin-tabs {
        flex-wrap: wrap;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .login-container {
        padding: 2rem;
    }

    .login-actions {
        flex-direction: column;
    }

    .login-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
