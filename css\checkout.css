/* Checkout Page Styles */

.checkout-section {
    min-height: 100vh;
    padding: 12rem 2rem 4rem;
    background: #f8f9fa;
}

.checkout-container {
    max-width: 1000px;
    margin: 0 auto;
}

/* Progress Steps */
.checkout-progress {
    display: flex;
    justify-content: center;
    margin-bottom: 4rem;
    position: relative;
}

.checkout-progress::before {
    content: '';
    position: absolute;
    top: 2rem;
    left: 25%;
    right: 25%;
    height: 2px;
    background: #e1e5e9;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
    max-width: 200px;
}

.step-number {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: #e1e5e9;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: var(--green);
    color: white;
}

.step.completed .step-number {
    background: #28a745;
    color: white;
}

.step-title {
    font-size: 1.4rem;
    color: #666;
    text-align: center;
}

.step.active .step-title {
    color: var(--green);
    font-weight: 600;
}

/* Checkout Content */
.checkout-content {
    background: white;
    border-radius: 1rem;
    padding: 4rem;
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.1);
}

.checkout-step {
    display: none;
}

.checkout-step.active {
    display: block;
}

.checkout-step h2 {
    font-size: 2.5rem;
    color: var(--black);
    margin-bottom: 3rem;
    text-align: center;
}

/* Order Items */
.order-items {
    margin-bottom: 3rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    border: 1px solid #e1e5e9;
    border-radius: 1rem;
    margin-bottom: 1rem;
}

.order-item img {
    width: 8rem;
    height: 8rem;
    object-fit: cover;
    border-radius: 0.5rem;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    font-size: 1.8rem;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.item-details p {
    color: var(--light-color);
    font-size: 1.4rem;
}

.item-price {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--green);
}

/* Order Summary */
.order-summary {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 3rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 1.6rem;
}

.summary-row.total {
    border-top: 2px solid #e1e5e9;
    padding-top: 1rem;
    font-size: 2rem;
    font-weight: 600;
    color: var(--black);
}

/* Forms */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--black);
}

.form-group input {
    width: 100%;
    padding: 1.5rem;
    border: 2px solid #e1e5e9;
    border-radius: 0.8rem;
    font-size: 1.6rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--green);
}

/* Delivery Options */
.delivery-options {
    margin-top: 3rem;
}

.delivery-options h3 {
    font-size: 2rem;
    color: var(--black);
    margin-bottom: 2rem;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    border: 2px solid #e1e5e9;
    border-radius: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.radio-option:hover {
    border-color: var(--green);
}

.radio-option input {
    display: none;
}

.radio-custom {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e1e5e9;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.radio-option input:checked + .radio-custom {
    border-color: var(--green);
    background: var(--green);
}

.radio-option input:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.8rem;
    height: 0.8rem;
    background: white;
    border-radius: 50%;
}

.option-details {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.option-details strong {
    color: var(--black);
    font-size: 1.6rem;
}

.option-details span:last-child {
    color: var(--green);
    font-weight: 600;
    font-size: 1.6rem;
}

/* Payment Methods */
.payment-methods {
    margin-bottom: 3rem;
}

.payment-option {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    border: 2px solid #e1e5e9;
    border-radius: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-option:hover {
    border-color: var(--green);
}

.payment-option .option-details {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.payment-option i {
    font-size: 2rem;
    color: var(--green);
}

/* Card Form */
.card-form {
    position: relative;
}

.card-icons {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 0.5rem;
}

.card-icons i {
    font-size: 2rem;
    color: #ccc;
}

/* Buttons */
.checkout-btn {
    background: var(--green);
    color: white;
    border: none;
    padding: 1.5rem 3rem;
    border-radius: 0.8rem;
    font-size: 1.6rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 1rem;
}

.checkout-btn:hover {
    background: #006400;
    transform: translateY(-2px);
}

.checkout-btn.secondary {
    background: #6c757d;
}

.checkout-btn.secondary:hover {
    background: #545b62;
}

.step-buttons {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
}

/* Confirmation */
.confirmation-content {
    text-align: center;
}

.success-icon {
    margin-bottom: 2rem;
}

.success-icon i {
    font-size: 8rem;
    color: #28a745;
}

.confirmation-content h3 {
    font-size: 3rem;
    color: var(--black);
    margin-bottom: 1rem;
}

.confirmation-content p {
    font-size: 1.8rem;
    color: var(--light-color);
    margin-bottom: 3rem;
}

.order-details {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 3rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 1.6rem;
}

.detail-row:last-child {
    border-top: 1px solid #e1e5e9;
    padding-top: 1rem;
    font-weight: 600;
    color: var(--black);
}

.confirmation-actions {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

/* Cart Styles */
.empty-cart {
    text-align: center;
    color: var(--light-color);
    font-size: 1.4rem;
    padding: 2rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.quantity-controls button {
    width: 3rem;
    height: 3rem;
    border: 1px solid #e1e5e9;
    background: white;
    border-radius: 0.3rem;
    cursor: pointer;
    font-size: 1.4rem;
    transition: all 0.3s ease;
}

.quantity-controls button:hover {
    background: var(--green);
    color: white;
    border-color: var(--green);
}

.quantity {
    font-size: 1.4rem;
    color: var(--light-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-progress {
        flex-wrap: wrap;
        gap: 2rem;
    }
    
    .checkout-content {
        padding: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .order-item {
        flex-direction: column;
        text-align: center;
    }
    
    .step-buttons {
        flex-direction: column;
    }
    
    .confirmation-actions {
        flex-direction: column;
    }
}
