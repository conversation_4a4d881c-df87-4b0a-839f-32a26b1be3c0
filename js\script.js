let searchForm = document.querySelector('.search-form');

document.querySelector('#search-btn').onclick = () =>
{
	searchForm.classList.toggle('active');
	shoppingCart.classList.remove('active');
	loginForm.classList.remove('active');
	navbar.classList.remove('active');

	// Focus on search input when opened
	if (searchForm.classList.contains('active')) {
		setTimeout(() => {
			const searchInput = document.getElementById('search-box');
			if (searchInput) {
				searchInput.focus();
			}
		}, 100);
	}
}


let shoppingCart = document.querySelector('.shopping-cart');

document.querySelector('#cart-btn').onclick = () =>
{
	shoppingCart.classList.toggle('active');
	searchForm.classList.remove('active');
    loginForm.classList.remove('active');
	navbar.classList.remove('active');
}



let loginForm = document.querySelector('.login-form');

document.querySelector('#login-btn').onclick = () =>
{
	loginForm.classList.toggle('active');
	shoppingCart.classList.remove('active');
	searchForm.classList.remove('active');
	navbar.classList.remove('active');
}



let navbar = document.querySelector('.navbar');

document.querySelector('#menu-btn').onclick = () =>
{
	navbar.classList.toggle('active');
	loginForm.classList.remove('active');
	shoppingCart.classList.remove('active');
	searchForm.classList.remove('active');
	
}



window.onscroll = () =>
{
	searchForm.classList.remove('active');
	shoppingCart.classList.remove('active');
	loginForm.classList.remove('active');
	navbar.classList.remove('active');
}



 var swiper = new Swiper(".product-slider", {
      loop:true,
      spaceBetween: 20,

      autoplay:{
      	delay: 7500,
      	disableOnInteraction: false,
      },

      breakpoints: {
        0: {
          slidesPerView: 1,
        },
        768: {
          slidesPerView: 2,
        },
        1020: {
          slidesPerView: 3,
        },
      },
    });

// Enhanced functionality for the website

// API base URL - change this when backend is running
const API_BASE_URL = 'http://localhost:3000/api';

// Check if backend is available
let backendAvailable = false;

async function checkBackend() {
    try {
        const response = await fetch(`${API_BASE_URL}/products`);
        backendAvailable = response.ok;
        console.log('Backend status:', backendAvailable ? 'Available' : 'Not available');
    } catch (error) {
        backendAvailable = false;
        console.log('Backend not available, using static data');
    }
}

// Load products dynamically if backend is available
async function loadProducts() {
    if (!backendAvailable) return;

    try {
        const response = await fetch(`${API_BASE_URL}/products`);
        const products = await response.json();

        // Update product slider with dynamic data
        updateProductSlider(products);
    } catch (error) {
        console.error('Error loading products:', error);
    }
}

// Update product slider with dynamic data
function updateProductSlider(products) {
    const swiperWrapper = document.querySelector('.swiper-wrapper');
    if (!swiperWrapper) return;

    swiperWrapper.innerHTML = '';

    products.forEach(product => {
        const slide = document.createElement('div');
        slide.className = 'swiper-slide box';
        slide.innerHTML = `
            <img src="${product.image}" alt="${product.name}">
            <h3>${product.name}</h3>
            <div class="price">$${product.price}</div>
            <div class="stars">
                ${generateStars(product.rating || 4.5)}
            </div>
            <a href="#" class="btn" onclick="addToCart(${product.id})">add to cart</a>
        `;
        swiperWrapper.appendChild(slide);
    });

    // Reinitialize Swiper
    if (window.swiper) {
        window.swiper.update();
    }
}

// Generate star rating HTML
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHTML = '';

    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fas fa-star"></i>';
    }

    if (hasHalfStar) {
        starsHTML += '<i class="fas fa-star-half-alt"></i>';
    }

    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star"></i>';
    }

    return starsHTML;
}

// Shopping cart functionality
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Sample products data for cart functionality
const sampleProducts = {
    1: { name: "Fresh Orange", price: 12.99, image: "image/product-1.png" },
    2: { name: "Fresh Onion", price: 15.99, image: "image/product-2.png" },
    3: { name: "Fresh Meat", price: 20.99, image: "image/product-3.png" },
    4: { name: "Fresh Cabbage", price: 8.99, image: "image/product-4.png" },
    5: { name: "Fresh Potato", price: 5.99, image: "image/product-5.png" },
    6: { name: "Fresh Avocado", price: 15.99, image: "image/product-6.png" },
    7: { name: "Fresh Carrot", price: 8.99, image: "image/product-1.png" },
    8: { name: "Fresh Lemon", price: 6.99, image: "image/product-2.png" },
    9: { name: "Fresh Watermelon", price: 18.99, image: "image/product-3.png" }
};

function addToCart(productId) {
    // Get product info
    const product = sampleProducts[productId];
    if (!product) {
        alert('Product not found!');
        return;
    }

    // Find existing item in cart
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: 1
        });
    }

    // Save to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Update cart display
    updateCartDisplay();

    // Show success message
    alert(`${product.name} added to cart!`);

    // Show cart dropdown
    const shoppingCart = document.querySelector('.shopping-cart');
    if (shoppingCart) {
        shoppingCart.classList.add('active');
    }
}

function updateCartDisplay() {
    const cartItems = document.getElementById('cart-items');
    const cartCount = document.getElementById('cart-count');
    const cartTotal = document.getElementById('cart-total');
    const checkoutBtn = document.getElementById('checkout-btn');
    
    if (!cartItems || !cartCount || !cartTotal) return;

    // Update cart count
    const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
    cartCount.textContent = totalItems;
    cartCount.style.display = totalItems > 0 ? 'flex' : 'none';

    // Update cart items
    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <p>Your cart is empty</p>
            </div>
        `;
        checkoutBtn.style.display = 'none';
    } else {
        cartItems.innerHTML = cart.map(item => `
            <div class="cart-item">
                <img src="${item.image}" alt="${item.name}">
                <div class="content">
                    <h3>${item.name}</h3>
                    <div class="price">$${item.price}</div>
                    <div class="quantity">
                        <button onclick="decreaseQuantity(${item.id})">-</button>
                        <span>${item.quantity}</span>
                        <button onclick="increaseQuantity(${item.id})">+</button>
                    </div>
                </div>
                <button class="remove-btn" onclick="removeFromCart(${item.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
        checkoutBtn.style.display = 'block';
    }

    // Update total
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cartTotal.textContent = `$${total.toFixed(2)}`;
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartDisplay();
}

function increaseQuantity(productId) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        item.quantity += 1;
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartDisplay();
    }
}

function decreaseQuantity(productId) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        item.quantity -= 1;
        if (item.quantity <= 0) {
            removeFromCart(productId);
        } else {
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartDisplay();
        }
    }
}

// Initialize cart display when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateCartDisplay();
    
    // Add click event listeners to all add to cart buttons
    const addToCartButtons = document.querySelectorAll('[data-product-id]');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = parseInt(this.getAttribute('data-product-id'));
            addToCart(productId);
        });
    });
});

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Search functionality
function initializeSearch() {
    const searchBox = document.getElementById('search-box');
    if (!searchBox) return;

    searchBox.addEventListener('input', debounce(handleSearch, 300));
}

function handleSearch(event) {
    const searchTerm = event.target.value.toLowerCase().trim();

    if (searchTerm.length < 2) {
        // Reset to show all products
        if (backendAvailable) {
            loadProducts();
        }
        return;
    }

    if (backendAvailable) {
        searchProductsAPI(searchTerm);
    } else {
        searchProductsLocal(searchTerm);
    }
}

async function searchProductsAPI(searchTerm) {
    try {
        const response = await fetch(`${API_BASE_URL}/products?search=${encodeURIComponent(searchTerm)}`);
        const products = await response.json();
        updateProductSlider(products);
    } catch (error) {
        console.error('Error searching products:', error);
    }
}

function searchProductsLocal(searchTerm) {
    const productBoxes = document.querySelectorAll('.swiper-slide.box');

    productBoxes.forEach(box => {
        const productName = box.querySelector('h3').textContent.toLowerCase();

        if (productName.includes(searchTerm)) {
            box.style.display = 'block';
        } else {
            box.style.display = 'none';
        }
    });
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    await checkBackend();

    if (backendAvailable) {
        await loadProducts();
    }

    initializeSearch();
    updateCartDisplay();
    setupShopNowButtons();
    setupAddToCartButtons();
    updateNavbarForUser();
    initializeScrollAnimations();
    initializeHeaderEffects();
    initializeNewsletterForm();

    console.log('FreshMart Pro initialized successfully!');
});

// Setup Shop Now buttons
function setupShopNowButtons() {
    // Shop Now button in hero section
    const shopNowBtn = document.querySelector('.home .content .btn');
    if (shopNowBtn) {
        shopNowBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // Scroll to products section
            const productsSection = document.getElementById('products');
            if (productsSection) {
                productsSection.scrollIntoView({ behavior: 'smooth' });
            } else {
                // If not on home page, go to home page products section
                window.location.href = 'index.html#products';
            }
        });
    }

    // Shop Now buttons in categories
    const categoryShopBtns = document.querySelectorAll('.categories .box .btn, .category-box .btn');
    categoryShopBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            // Get category from parent element
            const categoryBox = this.closest('.box');
            const categoryName = categoryBox ? categoryBox.querySelector('h3').textContent.toLowerCase() : 'all';

            // Redirect to categories page with filter
            window.location.href = `categories.html?category=${encodeURIComponent(categoryName)}`;
        });
    });

    // Read More buttons in features
    const readMoreBtns = document.querySelectorAll('.features .box .btn');
    readMoreBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            // Scroll to products or redirect to relevant page
            window.location.href = 'categories.html';
        });
    });
}

// Setup Add to Cart buttons
function setupAddToCartButtons() {
    // Add to cart buttons in product slider
    const addToCartBtns = document.querySelectorAll('.products .btn, .product-box .btn');
    addToCartBtns.forEach((btn, index) => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();

            // Get product ID from button or use index + 1
            let productId = this.dataset.productId || (index + 1);

            // If button is inside a product box, try to get ID from parent
            const productBox = this.closest('.box, .product-box');
            if (productBox) {
                const productImg = productBox.querySelector('img');
                if (productImg && productImg.src.includes('product-')) {
                    // Extract product number from image name
                    const match = productImg.src.match(/product-(\d+)/);
                    if (match) {
                        productId = parseInt(match[1]);
                    }
                }
            }

            addToCart(productId);
        });
    });
}

// Update navbar based on user login status
function updateNavbarForUser() {
    const navbarLogin = document.getElementById('navbar-login');
    const navbarAdmin = document.getElementById('navbar-admin');

    if (window.currentUser) {
        // User is logged in
        if (navbarLogin) {
            navbarLogin.style.display = 'none';
        }

        // Show admin link only for admin users
        if (navbarAdmin && window.currentUser.role === 'admin') {
            navbarAdmin.style.display = 'block';
        }
    } else {
        // User is not logged in
        if (navbarLogin) {
            navbarLogin.style.display = 'block';
        }
        if (navbarAdmin) {
            navbarAdmin.style.display = 'none';
        }
    }
}

// Enhanced add to cart function with better product detection
function addToCartEnhanced(element) {
    // Get product information from the clicked element's context
    const productBox = element.closest('.box, .product-box, .swiper-slide');

    if (!productBox) {
        showNotification('Product information not found!', 'error');
        return;
    }

    // Extract product details
    const productImg = productBox.querySelector('img');
    const productName = productBox.querySelector('h3, h1')?.textContent || 'Unknown Product';
    const productPriceElement = productBox.querySelector('.price');

    let productId = 1;
    let productPrice = 10.99;

    // Try to extract product ID from image
    if (productImg && productImg.src.includes('product-')) {
        const match = productImg.src.match(/product-(\d+)/);
        if (match) {
            productId = parseInt(match[1]);
        }
    }

    // Try to extract price
    if (productPriceElement) {
        const priceText = productPriceElement.textContent;
        const priceMatch = priceText.match(/\$?(\d+\.?\d*)/);
        if (priceMatch) {
            productPrice = parseFloat(priceMatch[1]);
        }
    }

    // Use the existing addToCart function
    addToCart(productId);
}

// Update the existing addToCart function to handle missing products better
const originalAddToCart = window.addToCart || addToCart;
window.addToCart = function(productId) {
    // Ensure productId is a number
    productId = parseInt(productId) || 1;

    // Get product info (use existing sampleProducts or create default)
    let product = sampleProducts[productId];

    if (!product) {
        // Create a default product if not found
        product = {
            name: `Product ${productId}`,
            price: 9.99,
            image: `image/product-${productId}.png`
        };
        sampleProducts[productId] = product;
    }

    // Find existing item in cart
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: 1,
            addedAt: new Date().toISOString()
        });
    }

    // Save to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Update cart display
    updateCartDisplay();

    // Show success message
    showNotification(`${product.name} added to cart!`, 'success');

    // Show cart dropdown briefly
    const shoppingCart = document.querySelector('.shopping-cart');
    if (shoppingCart) {
        shoppingCart.classList.add('active');
        // Auto-hide after 3 seconds
        setTimeout(() => {
            shoppingCart.classList.remove('active');
        }, 3000);
    }
};

// Scroll Animations
function initializeScrollAnimations() {
    const revealElements = document.querySelectorAll('.reveal');

    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('active');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    revealElements.forEach(element => {
        revealObserver.observe(element);
    });
}

// Header Effects
function initializeHeaderEffects() {
    const header = document.querySelector('.header');

    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Newsletter Form
function initializeNewsletterForm() {
    const newsletterForm = document.querySelector('.newsletter-form');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;

            if (email) {
                showNotification('Thank you for subscribing to our newsletter!', 'success');
                this.querySelector('input[type="email"]').value = '';

                // Here you would typically send the email to your backend
                if (backendAvailable) {
                    subscribeToNewsletter(email);
                }
            }
        });
    }
}

// Newsletter subscription API call
async function subscribeToNewsletter(email) {
    try {
        const response = await fetch(`${API_BASE_URL}/newsletter/subscribe`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email })
        });

        if (response.ok) {
            console.log('Newsletter subscription successful');
        }
    } catch (error) {
        console.error('Newsletter subscription error:', error);
    }
}

// Enhanced product interactions
function addProductHoverEffects() {
    const productBoxes = document.querySelectorAll('.products .box, .product-box');

    productBoxes.forEach(box => {
        box.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.05)';
        });

        box.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Smooth scrolling for navigation links (DISABLED for proper page navigation)
function initializeSmoothScrolling() {
    // Only apply smooth scrolling to links that are on the same page
    const navLinks = document.querySelectorAll('.navbar a[href^="#"]:not([href*="index.html"]');

    navLinks.forEach(link => {
        // Only add smooth scrolling if we're on the same page as the target
        const href = link.getAttribute('href');
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';

        // Only apply smooth scrolling for same-page navigation
        if (href.startsWith('#') && !href.includes('.html')) {
            link.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    e.preventDefault();
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // If target doesn't exist, let the default behavior happen (page navigation)
            });
        }
    });
}

// Enhanced cart functionality with animations
function animateCartUpdate() {
    const cartIcon = document.getElementById('cart-btn');
    const cartBadge = document.getElementById('cart-count');

    if (cartIcon && cartBadge) {
        cartIcon.style.animation = 'none';
        cartBadge.style.animation = 'none';

        setTimeout(() => {
            cartIcon.style.animation = 'bounce 0.6s ease';
            cartBadge.style.animation = 'bounce 0.6s ease';
        }, 10);
    }
}

// Add floating animation to feature images
function addFloatingAnimations() {
    const featureImages = document.querySelectorAll('.features .box img');

    featureImages.forEach((img, index) => {
        img.style.animationDelay = `${index * 0.5}s`;
        img.classList.add('float-animation');
    });
}

// Enhanced search icon functionality
function enhanceSearchIcon() {
    const searchIcon = document.querySelector('.search-icon');
    const searchInput = document.getElementById('search-box');

    if (searchIcon && searchInput) {
        searchIcon.addEventListener('click', function(e) {
            e.preventDefault();
            const searchTerm = searchInput.value.trim();

            if (searchTerm) {
                // Trigger search
                handleSearch({ target: searchInput });
                showNotification(`Searching for "${searchTerm}"...`, 'info');
            } else {
                // Focus on input if empty
                searchInput.focus();
                showNotification('Please enter a search term', 'warning');
            }
        });
    }
}

// Fix features section display issues
function fixFeaturesSection() {
    const featureBoxes = document.querySelectorAll('.features .box');

    featureBoxes.forEach((box, index) => {
        // Add staggered animation delay
        box.style.animationDelay = `${index * 0.2}s`;

        // Ensure proper display
        box.style.display = 'block';
        box.style.visibility = 'visible';
        box.style.opacity = '1';
    });
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    // initializeSmoothScrolling(); // Disabled for proper page navigation
    addProductHoverEffects();
    enhanceSearchIcon();
    fixFeaturesSection();
    setTimeout(addFloatingAnimations, 1000);
});