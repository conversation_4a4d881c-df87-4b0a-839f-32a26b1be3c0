// Checkout JavaScript

let currentStep = 1;
let orderData = {};

document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    if (!window.currentUser) {
        showNotification('Please login to proceed with checkout', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return;
    }

    // Check if cart has items
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    if (cart.length === 0) {
        showNotification('Your cart is empty', 'warning');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
        return;
    }

    initializeCheckout();
    loadOrderItems();
    setupEventListeners();
});

function initializeCheckout() {
    // Pre-fill shipping information from user profile
    if (window.currentUser) {
        document.getElementById('shipping-first-name').value = window.currentUser.firstName || '';
        document.getElementById('shipping-last-name').value = window.currentUser.lastName || '';
        document.getElementById('shipping-email').value = window.currentUser.email || '';
        document.getElementById('shipping-phone').value = window.currentUser.phone || '';
        
        if (window.currentUser.address) {
            document.getElementById('shipping-address').value = window.currentUser.address;
        }
    }
}

function setupEventListeners() {
    // Payment method change
    const paymentMethods = document.querySelectorAll('input[name="payment"]');
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            const cardForm = document.getElementById('card-form');
            if (this.value === 'card') {
                cardForm.style.display = 'block';
            } else {
                cardForm.style.display = 'none';
            }
        });
    });

    // Delivery option change
    const deliveryOptions = document.querySelectorAll('input[name="delivery"]');
    deliveryOptions.forEach(option => {
        option.addEventListener('change', updateShippingCost);
    });

    // Card number formatting
    const cardNumberInput = document.getElementById('card-number');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', formatCardNumber);
    }

    // Expiry date formatting
    const cardExpiryInput = document.getElementById('card-expiry');
    if (cardExpiryInput) {
        cardExpiryInput.addEventListener('input', formatExpiryDate);
    }
}

function loadOrderItems() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const orderItemsContainer = document.getElementById('order-items');
    
    orderItemsContainer.innerHTML = '';
    
    cart.forEach(item => {
        const orderItem = document.createElement('div');
        orderItem.className = 'order-item';
        orderItem.innerHTML = `
            <img src="${item.image}" alt="${item.name}">
            <div class="item-details">
                <h4>${item.name}</h4>
                <p>Quantity: ${item.quantity}</p>
            </div>
            <div class="item-price">$${(item.price * item.quantity).toFixed(2)}</div>
        `;
        orderItemsContainer.appendChild(orderItem);
    });

    updateOrderSummary();
}

function updateOrderSummary() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    // Get shipping cost
    const selectedDelivery = document.querySelector('input[name="delivery"]:checked');
    let shippingCost = 5.99; // Default standard shipping
    
    if (selectedDelivery) {
        switch (selectedDelivery.value) {
            case 'express':
                shippingCost = 12.99;
                break;
            case 'overnight':
                shippingCost = 24.99;
                break;
            default:
                shippingCost = 5.99;
        }
    }
    
    const tax = subtotal * 0.08; // 8% tax
    const total = subtotal + shippingCost + tax;
    
    // Update display
    document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
    document.getElementById('shipping').textContent = `$${shippingCost.toFixed(2)}`;
    document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
    document.getElementById('final-total').textContent = `$${total.toFixed(2)}`;
    
    // Store order data
    orderData = {
        items: cart,
        subtotal: subtotal,
        shipping: shippingCost,
        tax: tax,
        total: total
    };
}

function updateShippingCost() {
    updateOrderSummary();
}

function nextStep() {
    if (validateCurrentStep()) {
        currentStep++;
        updateStepDisplay();
        
        if (currentStep === 4) {
            processOrder();
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
    }
}

function updateStepDisplay() {
    // Update progress steps
    const steps = document.querySelectorAll('.step');
    steps.forEach((step, index) => {
        step.classList.remove('active', 'completed');
        if (index + 1 === currentStep) {
            step.classList.add('active');
        } else if (index + 1 < currentStep) {
            step.classList.add('completed');
        }
    });
    
    // Update step content
    const stepContents = document.querySelectorAll('.checkout-step');
    stepContents.forEach((content, index) => {
        content.classList.remove('active');
        if (index + 1 === currentStep) {
            content.classList.add('active');
        }
    });
}

function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            // Cart review - always valid if we got here
            return true;
            
        case 2:
            // Shipping information validation
            const requiredFields = [
                'shipping-first-name',
                'shipping-last-name',
                'shipping-email',
                'shipping-phone',
                'shipping-address',
                'shipping-city',
                'shipping-state',
                'shipping-zip'
            ];
            
            for (let fieldId of requiredFields) {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    showNotification(`Please fill in ${field.previousElementSibling.textContent}`, 'error');
                    field.focus();
                    return false;
                }
            }
            
            // Email validation
            const email = document.getElementById('shipping-email').value;
            if (!isValidEmail(email)) {
                showNotification('Please enter a valid email address', 'error');
                return false;
            }
            
            return true;
            
        case 3:
            // Payment validation
            const paymentMethod = document.querySelector('input[name="payment"]:checked').value;
            
            if (paymentMethod === 'card') {
                const cardNumber = document.getElementById('card-number').value;
                const cardExpiry = document.getElementById('card-expiry').value;
                const cardCvv = document.getElementById('card-cvv').value;
                const cardName = document.getElementById('card-name').value;
                
                if (!cardNumber || !cardExpiry || !cardCvv || !cardName) {
                    showNotification('Please fill in all card details', 'error');
                    return false;
                }
                
                if (cardNumber.replace(/\s/g, '').length < 16) {
                    showNotification('Please enter a valid card number', 'error');
                    return false;
                }
            }
            
            return true;
            
        default:
            return true;
    }
}

function processOrder() {
    // Collect all order information
    const shippingInfo = {
        firstName: document.getElementById('shipping-first-name').value,
        lastName: document.getElementById('shipping-last-name').value,
        email: document.getElementById('shipping-email').value,
        phone: document.getElementById('shipping-phone').value,
        address: document.getElementById('shipping-address').value,
        city: document.getElementById('shipping-city').value,
        state: document.getElementById('shipping-state').value,
        zip: document.getElementById('shipping-zip').value
    };
    
    const deliveryOption = document.querySelector('input[name="delivery"]:checked').value;
    const paymentMethod = document.querySelector('input[name="payment"]:checked').value;
    
    // Generate order number
    const orderNumber = 'ORD-' + Date.now().toString().slice(-6);
    
    // Calculate delivery date
    let deliveryDays = 7;
    switch (deliveryOption) {
        case 'express':
            deliveryDays = 3;
            break;
        case 'overnight':
            deliveryDays = 1;
            break;
    }
    
    const deliveryDate = new Date();
    deliveryDate.setDate(deliveryDate.getDate() + deliveryDays);
    
    // Create order object
    const order = {
        orderNumber: orderNumber,
        customerId: window.currentUser.id,
        customerName: `${shippingInfo.firstName} ${shippingInfo.lastName}`,
        items: orderData.items,
        shippingInfo: shippingInfo,
        deliveryOption: deliveryOption,
        paymentMethod: paymentMethod,
        subtotal: orderData.subtotal,
        shipping: orderData.shipping,
        tax: orderData.tax,
        total: orderData.total,
        orderDate: new Date().toISOString(),
        estimatedDelivery: deliveryDate.toISOString(),
        status: 'confirmed'
    };
    
    // Save order to localStorage (in real app, send to backend)
    let orders = JSON.parse(localStorage.getItem('orders')) || [];
    orders.push(order);
    localStorage.setItem('orders', JSON.stringify(orders));
    
    // Update confirmation display
    document.getElementById('order-number').textContent = orderNumber;
    document.getElementById('delivery-date').textContent = `${deliveryDays} business day${deliveryDays > 1 ? 's' : ''}`;
    document.getElementById('order-total').textContent = `$${orderData.total.toFixed(2)}`;
    
    // Clear cart
    localStorage.removeItem('cart');
    
    // Update cart display
    if (window.updateCartDisplay) {
        window.updateCartDisplay();
    }
    
    showNotification('Order placed successfully!', 'success');
}

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function formatCardNumber(e) {
    let value = e.target.value.replace(/\s/g, '');
    let formattedValue = value.replace(/(.{4})/g, '$1 ').trim();
    e.target.value = formattedValue;
}

function formatExpiryDate(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
    }
    e.target.value = value;
}

// Export functions for global use
window.nextStep = nextStep;
window.prevStep = prevStep;
