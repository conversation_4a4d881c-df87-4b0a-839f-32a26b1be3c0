<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Redirecting to Admin Panel - Glossary Store</title>

	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">

	<style>
		.redirect-section {
			min-height: 100vh;
			display: flex;
			align-items: center;
			justify-content: center;
			background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
			text-align: center;
		}

		.redirect-container {
			background: white;
			padding: 4rem;
			border-radius: 2rem;
			box-shadow: 0 2rem 4rem rgba(0,0,0,0.1);
			max-width: 500px;
		}

		.redirect-icon {
			font-size: 6rem;
			color: var(--green);
			margin-bottom: 2rem;
		}

		.redirect-title {
			font-size: 3rem;
			color: var(--black);
			margin-bottom: 1rem;
		}

		.redirect-message {
			font-size: 1.6rem;
			color: var(--light-color);
			margin-bottom: 2rem;
		}

		.countdown {
			font-size: 2rem;
			color: var(--green);
			font-weight: bold;
			margin-bottom: 2rem;
		}

		.redirect-btn {
			background: var(--green);
			color: white;
			padding: 1.5rem 3rem;
			border: none;
			border-radius: 1rem;
			font-size: 1.6rem;
			cursor: pointer;
			text-decoration: none;
			display: inline-block;
			transition: all 0.3s ease;
		}

		.redirect-btn:hover {
			background: #006400;
			transform: translateY(-2px);
		}
	</style>
</head>
<body>

<!-- redirect section -->
<section class="redirect-section">
	<div class="redirect-container">
		<div class="redirect-icon">
			<i class="fas fa-cogs"></i>
		</div>
		<h1 class="redirect-title">Management Moved!</h1>
		<p class="redirect-message">
			The management features have been integrated into the Admin Panel for better organization.
		</p>
		<div class="countdown" id="countdown">Redirecting in 5 seconds...</div>
		<a href="admin.html" class="redirect-btn">
			<i class="fas fa-arrow-right"></i> Go to Admin Panel Now
		</a>
	</div>
</section>

<script>
// Auto redirect after 5 seconds
let countdown = 5;
const countdownElement = document.getElementById('countdown');

const timer = setInterval(() => {
	countdown--;
	countdownElement.textContent = `Redirecting in ${countdown} seconds...`;

	if (countdown <= 0) {
		clearInterval(timer);
		window.location.href = 'admin.html';
	}
}, 1000);

// Immediate redirect if user clicks anywhere
document.addEventListener('click', () => {
	window.location.href = 'admin.html';
});
</script>



</body>
</html>
