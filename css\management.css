/* Management Page Styles */

.management-section {
    min-height: 100vh;
    padding: 12rem 2rem 4rem;
    background: #f8f9fa;
}

.management-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Management Tabs */
.management-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    justify-content: center;
    flex-wrap: wrap;
}

.management-tabs .tab-btn {
    background: white;
    border: 2px solid #e1e5e9;
    padding: 1.5rem 2.5rem;
    border-radius: 1rem;
    cursor: pointer;
    font-size: 1.6rem;
    color: var(--light-color);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.management-tabs .tab-btn.active {
    background: var(--green);
    color: white;
    border-color: var(--green);
}

.management-tabs .tab-btn:hover {
    border-color: var(--green);
    color: var(--green);
}

.management-tabs .tab-btn.active:hover {
    color: white;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.content-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0,0,0,0.15);
}

.content-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 2rem;
}

.card-content h3 {
    font-size: 1.8rem;
    color: var(--black);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.card-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: var(--light-color);
}

.card-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-excerpt {
    font-size: 1.4rem;
    color: var(--light-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.card-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.action-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 1.3rem;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #007bff;
    color: white;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-approve {
    background: #28a745;
    color: white;
}

.btn-reject {
    background: #ffc107;
    color: #212529;
}

.action-btn:hover {
    transform: translateY(-2px);
    opacity: 0.9;
}

/* Reviews List */
.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-top: 2rem;
}

.review-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.1);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reviewer-avatar {
    width: 5rem;
    height: 5rem;
    border-radius: 50%;
    background: var(--green);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.reviewer-details h4 {
    font-size: 1.6rem;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.reviewer-details p {
    font-size: 1.2rem;
    color: var(--light-color);
}

.review-rating {
    display: flex;
    gap: 0.3rem;
}

.review-rating i {
    font-size: 1.6rem;
    color: #ffc107;
}

.review-content {
    font-size: 1.5rem;
    color: var(--light-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.review-status {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.setting-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.1);
}

.setting-card h3 {
    font-size: 2rem;
    color: var(--black);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.setting-card h3 i {
    color: var(--green);
}

.setting-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.current-logo {
    text-align: center;
    margin-bottom: 1rem;
}

.current-logo img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 1rem;
    border: 2px solid #e1e5e9;
}

.current-logo p {
    margin-top: 1rem;
    color: var(--light-color);
    font-size: 1.4rem;
}

.logo-upload {
    text-align: center;
}

.logo-upload input[type="file"] {
    display: none;
}

.logo-upload label {
    display: inline-block;
    cursor: pointer;
}

.color-picker {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.color-picker label {
    font-size: 1.4rem;
    color: var(--black);
    font-weight: 600;
    min-width: 120px;
}

.color-picker input[type="color"] {
    width: 5rem;
    height: 4rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
}

.settings-actions {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e1e5e9;
}

/* Filter Options */
.filter-options {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-options select {
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 0.5rem;
    font-size: 1.4rem;
    background: white;
}

/* Logo in Header */
.logo-img {
    height: 4rem;
    width: auto;
    margin-right: 1rem;
    border-radius: 0.5rem;
}

.header .logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header .logo span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.status-published {
    background: #d4edda;
    color: #155724;
}

.status-draft {
    background: #fff3cd;
    color: #856404;
}

/* Responsive Design */
@media (max-width: 768px) {
    .management-tabs {
        flex-direction: column;
    }
    
    .management-tabs .tab-btn {
        justify-content: center;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .card-actions {
        flex-wrap: wrap;
    }
    
    .review-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .settings-actions {
        flex-direction: column;
    }
    
    .color-picker {
        flex-direction: column;
        align-items: flex-start;
    }
}
