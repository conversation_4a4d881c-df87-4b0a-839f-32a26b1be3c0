<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Login - Glossary Store</title>
	
	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	
	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">
	<link rel="stylesheet" type="text/css" href="css/auth.css">
</head>
<body>

<!-- header section  -->
<header class="header"> 
    <a href="index.html" class="logo"><i class="fas fa-shopping-basket"></i> Glossary Store</a>

<nav class="navbar">
	<a href="index.html">home</a>
	<a href="index.html#features">features</a>
	<a href="index.html#products">products</a>
	<a href="categories.html">categories</a>
	<a href="reviews.html">reviews</a>
	<a href="blogs.html">blogs</a>

	<div class="admin-section">
		<a href="admin.html" class="admin-panel-link" id="navbar-admin" style="display: none;">
			<i class="fas fa-cogs"></i> Admin Panel
		</a>
	</div>
</nav>

<div class="icons">
	<div class="fas fa-bars" id="menu-btn" title="Menu">
		<i class="fas fa-bars"></i>
	</div>
	<div class="fas fa-search" id="search-btn" title="Search Products">
		<i class="fas fa-search"></i>
	</div>
	<div class="fas fa-shopping-cart" id="cart-btn" title="Shopping Cart">
		<i class="fas fa-shopping-cart"></i>
		<span class="cart-count" id="cart-count">0</span>
	</div>
	<div class="fas fa-user" id="login-btn" title="User Account">
		<i class="fas fa-user"></i>
	</div>
</div>

<form class="search-form">
	<input type="search" id="search-box" placeholder="Search products...">
	<label for="search-box" class="fas fa-search"></label>
</form>

<div class="shopping-cart">
	<div id="cart-items">
		<!-- Cart items will be loaded here -->
	</div>
	<div class="Total" id="cart-total">Total: $0.00</div>
	<a href="checkout.html" class="btn" id="checkout-btn">Checkout</a>
</div>



</header>
<!-- header section  -->

<!-- login section -->
<section class="auth-section">
	<div class="auth-container">
		<div class="auth-box">
			<div class="auth-header">
				<h2>Welcome Back!</h2>
				<p>Sign in to your account to continue shopping</p>
			</div>

			<form class="auth-form" id="login-form">
				<div class="form-group">
					<label for="email">Email Address</label>
					<input type="email" id="email" name="email" required>
					<i class="fas fa-envelope"></i>
				</div>

				<div class="form-group">
					<label for="password">Password</label>
					<input type="password" id="password" name="password" required>
					<i class="fas fa-lock"></i>
					<span class="password-toggle" onclick="togglePassword()">
						<i class="fas fa-eye" id="password-eye"></i>
					</span>
				</div>

				<div class="form-options">
					<label class="checkbox-container">
						<input type="checkbox" id="remember-me">
						<span class="checkmark"></span>
						Remember me
					</label>
					<a href="forgot-password.html" class="forgot-link">Forgot Password?</a>
				</div>

				<button type="submit" class="auth-btn">
					<i class="fas fa-sign-in-alt"></i>
					Sign In
				</button>

				<div class="divider">
					<span>or</span>
				</div>

				<div class="social-login">
					<button type="button" class="social-btn google-btn">
						<i class="fab fa-google"></i>
						Continue with Google
					</button>
					<button type="button" class="social-btn facebook-btn">
						<i class="fab fa-facebook-f"></i>
						Continue with Facebook
					</button>
				</div>

				<div class="auth-footer">
					<p>Don't have an account? <a href="register.html">Sign up here</a></p>
				</div>
			</form>
		</div>

		<div class="auth-image">
			<img src="image/banner-img.jpg" alt="Shopping">
			<div class="image-overlay">
				<h3>Fresh & Organic</h3>
				<p>Quality products delivered to your doorstep</p>
			</div>
		</div>
	</div>
</section>

<!-- Quick Login Demo -->
<section class="demo-section">
	<div class="demo-container">
		<h3>Quick Demo Login</h3>
		<p>Use these credentials for testing:</p>
		<div class="demo-credentials">
			<div class="demo-card">
				<h4>Customer Account</h4>
				<p><strong>Email:</strong> <EMAIL></p>
				<p><strong>Password:</strong> demo123</p>
				<button class="demo-btn" onclick="fillDemoCredentials('customer')">Use These</button>
			</div>
			<div class="demo-card">
				<h4>Admin Account</h4>
				<p><strong>Email:</strong> <EMAIL></p>
				<p><strong>Password:</strong> admin123</p>
				<button class="demo-btn" onclick="fillDemoCredentials('admin')">Use These</button>
			</div>
		</div>
	</div>
</section>

<script src="js/script.js"></script>
<script src="js/auth.js"></script>

</body>
</html>
