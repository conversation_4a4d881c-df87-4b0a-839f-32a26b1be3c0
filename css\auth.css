/* Authentication Pages Styles */

.auth-section {
    min-height: 100vh;
    padding: 12rem 2rem 4rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.auth-container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.auth-box {
    background: white;
    padding: 4rem;
    border-radius: 2rem;
    box-shadow: 0 2rem 4rem rgba(0,0,0,0.1);
}

.auth-header {
    text-align: center;
    margin-bottom: 3rem;
}

.auth-header h2 {
    font-size: 3rem;
    color: var(--black);
    margin-bottom: 1rem;
}

.auth-header p {
    font-size: 1.6rem;
    color: var(--light-color);
}

.auth-form {
    width: 100%;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--black);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1.5rem 1.5rem 1.5rem 5rem;
    border: 2px solid #e1e5e9;
    border-radius: 1rem;
    font-size: 1.6rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--green);
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 128, 0, 0.1);
}

.form-group i {
    position: absolute;
    left: 1.8rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.8rem;
    color: var(--light-color);
    transition: color 0.3s ease;
}

.form-group input:focus + i {
    color: var(--green);
}

.password-toggle {
    position: absolute;
    right: 1.8rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: 1.8rem;
    color: var(--light-color);
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--green);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    font-size: 1.4rem;
    color: var(--light-color);
    cursor: pointer;
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 2rem;
    height: 2rem;
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    border-radius: 0.4rem;
    margin-right: 1rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: var(--green);
    border-color: var(--green);
}

.checkbox-container input:checked + .checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.2rem;
}

.forgot-link {
    color: var(--green);
    text-decoration: none;
    font-size: 1.4rem;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: var(--black);
}

.auth-btn {
    width: 100%;
    padding: 1.8rem;
    background: var(--green);
    color: white;
    border: none;
    border-radius: 1rem;
    font-size: 1.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.auth-btn:hover {
    background: #006400;
    transform: translateY(-2px);
    box-shadow: 0 1rem 2rem rgba(0, 128, 0, 0.3);
}

.auth-btn i {
    margin-right: 1rem;
}

.divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.divider span {
    background: white;
    padding: 0 2rem;
    color: var(--light-color);
    font-size: 1.4rem;
}

.social-login {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-btn {
    flex: 1;
    padding: 1.5rem;
    border: 2px solid #e1e5e9;
    border-radius: 1rem;
    background: white;
    font-size: 1.4rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.google-btn:hover {
    border-color: #db4437;
    color: #db4437;
}

.facebook-btn:hover {
    border-color: #3b5998;
    color: #3b5998;
}

.social-btn i {
    margin-right: 1rem;
}

.auth-footer {
    text-align: center;
}

.auth-footer p {
    font-size: 1.4rem;
    color: var(--light-color);
}

.auth-footer a {
    color: var(--green);
    text-decoration: none;
    font-weight: 600;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Password Strength Meter */
.password-strength {
    margin-bottom: 2rem;
}

.strength-meter {
    width: 100%;
    height: 0.5rem;
    background: #e1e5e9;
    border-radius: 0.25rem;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-bar {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 0.25rem;
}

.strength-text {
    font-size: 1.2rem;
    color: var(--light-color);
}

/* Auth Image */
.auth-image {
    position: relative;
    border-radius: 2rem;
    overflow: hidden;
    height: 60rem;
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 128, 0, 0.8), rgba(0, 100, 0, 0.6));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    padding: 4rem;
}

.image-overlay h3 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
}

.image-overlay p {
    font-size: 1.8rem;
    margin-bottom: 3rem;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    font-size: 1.6rem;
}

.feature i {
    font-size: 2rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 1rem;
    border-radius: 50%;
}

/* Demo Section */
.demo-section {
    background: white;
    padding: 4rem 2rem;
}

.demo-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.demo-container h3 {
    font-size: 2.5rem;
    color: var(--black);
    margin-bottom: 1rem;
}

.demo-container p {
    font-size: 1.6rem;
    color: var(--light-color);
    margin-bottom: 3rem;
}

.demo-credentials {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.demo-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 1rem;
    border: 2px solid #e1e5e9;
}

.demo-card h4 {
    font-size: 1.8rem;
    color: var(--black);
    margin-bottom: 1rem;
}

.demo-card p {
    font-size: 1.4rem;
    color: var(--light-color);
    margin-bottom: 0.5rem;
}

.demo-btn {
    background: var(--green);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 1.4rem;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.demo-btn:hover {
    background: #006400;
    transform: translateY(-2px);
}

/* Cart Badge */
.cart-badge {
    position: absolute;
    top: -0.5rem;
    right: -0.5rem;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
}

/* User Profile Dropdown */
.user-profile {
    position: absolute;
    top: 110%;
    right: -110%;
    width: 30rem;
    background: white;
    border-radius: 1rem;
    box-shadow: var(--box-shadow);
    padding: 2rem;
    transition: all 0.4s ease;
}

.user-profile.active {
    right: 2rem;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e1e5e9;
    margin-bottom: 2rem;
}

.avatar i {
    font-size: 4rem;
    color: var(--green);
}

.user-details h4 {
    font-size: 1.6rem;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.user-details p {
    font-size: 1.2rem;
    color: var(--light-color);
}

.profile-menu a {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    color: var(--light-color);
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.profile-menu a:hover {
    background: #f8f9fa;
    color: var(--green);
}

.profile-menu a i {
    font-size: 1.6rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .auth-box {
        padding: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .social-login {
        flex-direction: column;
    }
    
    .demo-credentials {
        grid-template-columns: 1fr;
    }
    
    .auth-image {
        height: 40rem;
    }
    
    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
