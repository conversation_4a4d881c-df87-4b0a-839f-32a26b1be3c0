// Management Page JavaScript

// Sample data for blogs and reviews
let blogs = JSON.parse(localStorage.getItem('blogs')) || [
    {
        id: 1,
        title: "Benefits of Organic Food",
        author: "Admin",
        category: "Health",
        excerpt: "Discover the amazing health benefits of choosing organic foods for you and your family.",
        content: "Organic food has become increasingly popular as people become more health-conscious. Here are the key benefits...",
        image: "image/blog-1.jpg",
        date: "2024-01-15",
        published: true,
        tags: ["organic", "health", "nutrition"]
    },
    {
        id: 2,
        title: "Seasonal Vegetables Guide",
        author: "Nutritionist",
        category: "Nutrition",
        excerpt: "A comprehensive guide to seasonal vegetables and how to incorporate them into your daily meals.",
        content: "Eating seasonal vegetables is not only better for your health but also for the environment...",
        image: "image/blog-2.jpg",
        date: "2024-01-10",
        published: true,
        tags: ["vegetables", "seasonal", "cooking"]
    }
];

let reviews = JSON.parse(localStorage.getItem('reviews')) || [
    {
        id: 1,
        customerName: "<PERSON>",
        email: "<EMAIL>",
        productName: "Fresh Orange",
        rating: 5,
        comment: "Amazing quality products! Fresh vegetables and fruits delivered right to my doorstep.",
        date: "2024-01-15",
        status: "approved"
    },
    {
        id: 2,
        customerName: "<PERSON>",
        email: "<EMAIL>",
        productName: "Fresh Meat",
        rating: 4,
        comment: "Great service and competitive prices. The organic fruits are really fresh and tasty.",
        date: "2024-01-12",
        status: "pending"
    },
    {
        id: 3,
        customerName: "John Doe",
        email: "<EMAIL>",
        productName: "Fresh Avocado",
        rating: 2,
        comment: "The delivery was late and some items were not fresh.",
        date: "2024-01-10",
        status: "pending"
    }
];

// Initialize management page
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for auth.js to load and set currentUser
    setTimeout(() => {
        checkManagementAccess();
    }, 100);

    initializeManagement();
    loadBlogs();
    loadReviews();
    setupEventListeners();
});

function checkManagementAccess() {
    // Check if user is logged in and is admin
    if (!window.currentUser) {
        console.log('No user logged in for management access');
        return;
    }

    if (window.currentUser.role !== 'admin') {
        console.log('User is not admin, management features limited');
        return;
    }

    console.log('Admin access confirmed for management features');
}

function initializeManagement() {
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            switchManagementTab(tabName);
        });
    });
}

function setupEventListeners() {
    // Add blog button
    const addBlogBtn = document.getElementById('add-blog-btn');
    if (addBlogBtn) {
        addBlogBtn.addEventListener('click', () => openBlogModal());
    }

    // Blog modal
    const blogModal = document.getElementById('blog-modal');
    const closeBtn = blogModal.querySelector('.close');
    const cancelBtn = blogModal.querySelector('.btn-cancel');

    closeBtn.addEventListener('click', () => closeBlogModal());
    cancelBtn.addEventListener('click', () => closeBlogModal());

    // Blog form submission
    const blogForm = document.getElementById('blog-form');
    blogForm.addEventListener('submit', handleBlogSubmit);

    // Review filter
    const reviewFilter = document.getElementById('review-filter');
    reviewFilter.addEventListener('change', filterReviews);

    // Logo upload
    const logoUpload = document.getElementById('logo-upload');
    logoUpload.addEventListener('change', handleLogoUpload);

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === blogModal) {
            closeBlogModal();
        }
    });
}

function switchManagementTab(tabName) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // Add active class to selected tab and content
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// Blog Management Functions
function loadBlogs() {
    const blogsGrid = document.getElementById('blogs-grid');
    blogsGrid.innerHTML = '';

    blogs.forEach(blog => {
        const blogCard = document.createElement('div');
        blogCard.className = 'content-card';
        blogCard.innerHTML = `
            <img src="${blog.image}" alt="${blog.title}">
            <div class="card-content">
                <h3>${blog.title}</h3>
                <div class="card-meta">
                    <span><i class="fas fa-user"></i> ${blog.author}</span>
                    <span><i class="fas fa-calendar"></i> ${new Date(blog.date).toLocaleDateString()}</span>
                    <span><i class="fas fa-tag"></i> ${blog.category}</span>
                </div>
                <p class="card-excerpt">${blog.excerpt}</p>
                <div class="status-badge ${blog.published ? 'status-published' : 'status-draft'}">
                    ${blog.published ? 'Published' : 'Draft'}
                </div>
                <div class="card-actions">
                    <button class="action-btn btn-edit" onclick="editBlog(${blog.id})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="action-btn btn-delete" onclick="deleteBlog(${blog.id})">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
        blogsGrid.appendChild(blogCard);
    });
}

function openBlogModal(blogId = null) {
    const modal = document.getElementById('blog-modal');
    const modalTitle = document.getElementById('blog-modal-title');
    const form = document.getElementById('blog-form');

    if (blogId) {
        // Edit mode
        const blog = blogs.find(b => b.id === blogId);
        modalTitle.textContent = 'Edit Blog';
        
        document.getElementById('blog-title').value = blog.title;
        document.getElementById('blog-author').value = blog.author;
        document.getElementById('blog-category').value = blog.category;
        document.getElementById('blog-excerpt').value = blog.excerpt;
        document.getElementById('blog-content').value = blog.content;
        document.getElementById('blog-tags').value = blog.tags.join(', ');
        document.getElementById('blog-published').checked = blog.published;
        
        form.dataset.editId = blogId;
    } else {
        // Add mode
        modalTitle.textContent = 'Add New Blog';
        form.reset();
        delete form.dataset.editId;
    }

    modal.style.display = 'block';
}

function closeBlogModal() {
    document.getElementById('blog-modal').style.display = 'none';
}

function handleBlogSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const blogData = {
        title: formData.get('blog-title') || document.getElementById('blog-title').value,
        author: formData.get('blog-author') || document.getElementById('blog-author').value,
        category: formData.get('blog-category') || document.getElementById('blog-category').value,
        excerpt: formData.get('blog-excerpt') || document.getElementById('blog-excerpt').value,
        content: formData.get('blog-content') || document.getElementById('blog-content').value,
        tags: (formData.get('blog-tags') || document.getElementById('blog-tags').value).split(',').map(tag => tag.trim()),
        published: document.getElementById('blog-published').checked,
        image: 'image/blog-default.jpg' // Default image
    };

    const editId = e.target.dataset.editId;
    
    if (editId) {
        // Update existing blog
        const blogIndex = blogs.findIndex(b => b.id === parseInt(editId));
        blogs[blogIndex] = { ...blogs[blogIndex], ...blogData };
        showNotification('Blog updated successfully!', 'success');
    } else {
        // Add new blog
        const newBlog = {
            id: Math.max(...blogs.map(b => b.id)) + 1,
            ...blogData,
            date: new Date().toISOString().split('T')[0]
        };
        blogs.push(newBlog);
        showNotification('Blog added successfully!', 'success');
    }

    localStorage.setItem('blogs', JSON.stringify(blogs));
    loadBlogs();
    closeBlogModal();
}

function editBlog(id) {
    openBlogModal(id);
}

function deleteBlog(id) {
    if (confirm('Are you sure you want to delete this blog?')) {
        blogs = blogs.filter(b => b.id !== id);
        localStorage.setItem('blogs', JSON.stringify(blogs));
        loadBlogs();
        showNotification('Blog deleted successfully!', 'success');
    }
}

// Review Management Functions
function loadReviews() {
    const reviewsList = document.getElementById('reviews-list');
    const filter = document.getElementById('review-filter').value;
    
    let filteredReviews = reviews;
    if (filter !== 'all') {
        filteredReviews = reviews.filter(review => review.status === filter);
    }

    reviewsList.innerHTML = '';

    filteredReviews.forEach(review => {
        const reviewCard = document.createElement('div');
        reviewCard.className = 'review-card';
        reviewCard.innerHTML = `
            <div class="review-header">
                <div class="reviewer-info">
                    <div class="reviewer-avatar">
                        ${review.customerName.charAt(0).toUpperCase()}
                    </div>
                    <div class="reviewer-details">
                        <h4>${review.customerName}</h4>
                        <p>${review.email}</p>
                        <p>Product: ${review.productName}</p>
                    </div>
                </div>
                <div class="review-rating">
                    ${generateStars(review.rating)}
                </div>
            </div>
            <div class="review-content">
                "${review.comment}"
            </div>
            <div class="review-status status-${review.status}">
                ${review.status.charAt(0).toUpperCase() + review.status.slice(1)}
            </div>
            <div class="card-actions">
                ${review.status === 'pending' ? `
                    <button class="action-btn btn-approve" onclick="approveReview(${review.id})">
                        <i class="fas fa-check"></i> Approve
                    </button>
                    <button class="action-btn btn-reject" onclick="rejectReview(${review.id})">
                        <i class="fas fa-times"></i> Reject
                    </button>
                ` : ''}
                <button class="action-btn btn-delete" onclick="deleteReview(${review.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;
        reviewsList.appendChild(reviewCard);
    });
}

function filterReviews() {
    loadReviews();
}

function approveReview(id) {
    const review = reviews.find(r => r.id === id);
    if (review) {
        review.status = 'approved';
        localStorage.setItem('reviews', JSON.stringify(reviews));
        loadReviews();
        showNotification('Review approved!', 'success');
    }
}

function rejectReview(id) {
    const review = reviews.find(r => r.id === id);
    if (review) {
        review.status = 'rejected';
        localStorage.setItem('reviews', JSON.stringify(reviews));
        loadReviews();
        showNotification('Review rejected!', 'info');
    }
}

function deleteReview(id) {
    if (confirm('Are you sure you want to delete this review?')) {
        reviews = reviews.filter(r => r.id !== id);
        localStorage.setItem('reviews', JSON.stringify(reviews));
        loadReviews();
        showNotification('Review deleted!', 'success');
    }
}

// Website Settings Functions
function handleLogoUpload(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const currentLogo = document.getElementById('current-logo');
            currentLogo.src = e.target.result;
            
            // Save to localStorage
            localStorage.setItem('websiteLogo', e.target.result);
            showNotification('Logo uploaded successfully!', 'success');
        };
        reader.readAsDataURL(file);
    }
}

function saveWebsiteSettings() {
    const settings = {
        title: document.getElementById('website-title').value,
        tagline: document.getElementById('website-tagline').value,
        primaryColor: document.getElementById('primary-color').value,
        secondaryColor: document.getElementById('secondary-color').value,
        fontFamily: document.getElementById('font-family').value,
        contactPhone: document.getElementById('contact-phone').value,
        contactEmail: document.getElementById('contact-email').value,
        contactAddress: document.getElementById('contact-address').value,
        socialFacebook: document.getElementById('social-facebook').value,
        socialTwitter: document.getElementById('social-twitter').value,
        socialInstagram: document.getElementById('social-instagram').value,
        socialYoutube: document.getElementById('social-youtube').value
    };

    localStorage.setItem('websiteSettings', JSON.stringify(settings));
    applyWebsiteSettings(settings);
    showNotification('Settings saved successfully!', 'success');
}

function applyWebsiteSettings(settings) {
    // Apply color changes
    document.documentElement.style.setProperty('--green', settings.primaryColor);
    document.documentElement.style.setProperty('--black', settings.secondaryColor);
    
    // Apply font family
    document.body.style.fontFamily = settings.fontFamily;
    
    // Update page title
    document.title = settings.title;
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default?')) {
        localStorage.removeItem('websiteSettings');
        localStorage.removeItem('websiteLogo');
        location.reload();
    }
}

// Utility function for generating stars
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHTML = '';
    
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fas fa-star"></i>';
    }
    
    if (hasHalfStar) {
        starsHTML += '<i class="fas fa-star-half-alt"></i>';
    }
    
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star"></i>';
    }
    
    return starsHTML;
}

// Load saved settings on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedSettings = localStorage.getItem('websiteSettings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        applyWebsiteSettings(settings);
        
        // Fill form fields
        Object.keys(settings).forEach(key => {
            const element = document.getElementById(key.replace(/([A-Z])/g, '-$1').toLowerCase());
            if (element) {
                element.value = settings[key];
            }
        });
    }
    
    // Load saved logo
    const savedLogo = localStorage.getItem('websiteLogo');
    if (savedLogo) {
        const currentLogo = document.getElementById('current-logo');
        if (currentLogo) {
            currentLogo.src = savedLogo;
        }
    }
});

// Export functions for global use
window.editBlog = editBlog;
window.deleteBlog = deleteBlog;
window.approveReview = approveReview;
window.rejectReview = rejectReview;
window.deleteReview = deleteReview;
window.saveWebsiteSettings = saveWebsiteSettings;
window.resetSettings = resetSettings;
